import { useState, useEffect } from 'react'
import useAuth from '@/tina/stores/authStore'
import { IonPage, IonContent } from '@ionic/react'
import { useHistory } from 'react-router-dom'
import loginText from '@/assets/login_text.png'
import logo from '@/assets/logo.png'
import LoadingPage from '@/components/LoadingPage'
import LoginForm from '@/components/LoginForm'
import RegisterForm from '@/components/RegisterForm'

const HomePage = () => {
  const [isLoading, setIsLoading] = useState(true)
  const [showRegister, setShowRegister] = useState(false)
  const auth = useAuth()
  const history = useHistory()

  useEffect(() => {
    // 检查登录状态
    const checkAuthStatus = async () => {
      try {
        if (auth.isLoggedIn()) {
          // 如果已登录，跳转到引导页面
          history.push('/conversation/1')
          return
        }
      } catch (error) {
        console.error('检查登录状态失败:', error)
      } finally {
        setIsLoading(false)
      }
    }

    checkAuthStatus()
  }, [history])

  const handleLoginSuccess = () => {
    // 登录成功后跳转到引导页面
    history.push('/conversation/1')
    // history.replace('/onboarding')
  }

  const handleRegisterSuccess = () => {
    // 注册成功后跳转到引导页面
    history.push('/onboarding')
  }

  if (isLoading) {
    return (
      <IonPage>
        <IonContent>
          <LoadingPage />
        </IonContent>
      </IonPage>
    )
  }

  return (
    <IonPage>
      <IonContent className='ion-no-padding'>
        <div className='mx-auto flex h-full w-full max-w-md flex-col'
             style={{ backgroundColor: 'rgba(208, 216, 220, 1)' }}
        >
          {/* 上半部分 - Logo 区域 */}
          <div
            className='flex flex-shrink-0 flex-col items-center justify-start pt-10'
            style={{
              backgroundColor: 'rgba(208, 216, 220, 1)',
            }}
          >
            <div className='mb-6'>
              <img
                src={logo}
                alt='Tina Logo'
                className='h-24   translate-x-10 object-contain'
              />
            </div>
          </div>

          {/* 下半部分 - 表单区域 */}
          <div
            className='flex flex-1 flex-col'
            style={{
              backgroundColor: '#F6F4EE',
              borderTopRightRadius: '70% 200px',
            }}
          >
            {/* 登录文字图片 */}
            <div className='flex-shrink-0 p-12' >
              <img
                src={loginText}
                alt='Login Text'
                className='h-8 object-contain'
              />
            </div>

            {/* 表单内容区域 */}
            <div className='flex flex-1 flex-col '>
              {showRegister ? (
                <RegisterForm
                  onSuccess={handleRegisterSuccess}
                  onSwitchToLogin={() => setShowRegister(false)}
                />
              ) : (
                <LoginForm
                  onSuccess={handleLoginSuccess}
                  onSwitchToRegister={() => setShowRegister(true)}
                />
              )}
            </div>
          </div>
        </div>
      </IonContent>
    </IonPage>
  )
}

export default HomePage
