import { useEffect, useState } from 'react'
import { IonContent, IonPage } from '@ionic/react'
import { useHistory } from 'react-router-dom'
import logo from '../../assets/logo.png'
import splashText from '../../assets/splash_text.png'
import useAuth from '../../tina/stores/authStore'

/**
 * 移动端启动页面组件
 * 根据设计稿实现启动页面UI和动画效果
 */
export default function SplashPage() {
  const [progress, setProgress] = useState(0)
  const [loadingText, setLoadingText] = useState('正在初始化...')
  const history = useHistory()
  const auth = useAuth()

  useEffect(() => {
    // 模拟启动加载过程
    const loadingSteps = [
      { progress: 20, text: '正在初始化...', delay: 500 },
      { progress: 45, text: '加载配置...', delay: 500 },
      { progress: 70, text: '连接服务器...', delay: 500 },
      { progress: 90, text: '准备就绪...', delay: 500 },
      { progress: 100, text: '启动完成', delay: 500 },
    ]

    let currentStep = 0
    const timer = setInterval(() => {
      if (currentStep < loadingSteps.length) {
        const step = loadingSteps[currentStep]
        setProgress(step.progress)
        setLoadingText(step.text)
        currentStep++
      } else {
        clearInterval(timer)
        // 启动完成后跳转
        // setTimeout(() => {
        //   if (auth.isLoggedIn()) {
        //     history.replace('/conversation/1')
        //   } else {
        //     history.replace('/home')
        //   }
        // }, 500)
      }
    }, 600)

    return () => clearInterval(timer)
  }, [history, auth])

  return (
    <IonPage>
      <IonContent>
        <div
          className='mx-auto flex h-full w-full flex-col items-center justify-center text-white sm:max-w-md'
          style={{
            background: `linear-gradient(180deg,
              rgba(255, 255, 255, 0.31) 0%,
              rgba(182, 194, 202, 0.59) 50%,
              rgba(181, 193, 201, 0.64) 100%)`,
          }}
        >
          {/* Logo 图片 */}
          <div className='animate-fade-in mt-10 mb-6'>
            <div
              className='rounded-full p-6'
              style={{
                backgroundColor: 'rgba(241, 233, 221, 1) ',
                borderRadius: '60px',
              }}
            >
              <img
                src={logo}
                alt='Tina Logo'
                className='w-48 h-28 object-contain'
              />
            </div>
          </div>

          {/* 文字图片 */}
          <div className='animate-fade-in mb-12'>
            <img
              src={splashText}
              alt='Tina Text'
              className='h-8 object-contain'
            />
          </div>

          {/* 加载动画 */}
          <div className='animate-fade-in mb-8'>
            <div className='border-3 h-10 w-10 animate-spin rounded-full border-white border-t-gray-300'></div>
          </div>

          {/* 分块式进度条 */}
          <div className='animate-fade-in flex space-x-3'>
            {[1, 2, 3, 4, 5].map((block) => (
              <div
                key={block}
                className='h-4 w-7 rounded-full transition-all duration-300 ease-out'
                style={{
                  boxShadow: '0px 2px 20px 0px rgba(125,125,123,1)',
                  backgroundColor:
                    progress >= block * 20
                      ? 'rgba(239,236,227,1)'
                      : 'rgba(255, 255, 255, 0.3)',
                }}
              />
            ))}
          </div>

          {/*<p className='animate-fade-in text-sm text-white opacity-90'>*/}
          {/*  {loadingText}*/}
          {/*</p>*/}

          {/* 底部版权信息 */}
          <div className='animate-fade-in absolute bottom-8 w-full text-center'>
            <p className='text-xs text-white opacity-75'>
              © 2025 Tina Chat. All rights reserved.
            </p>
          </div>
        </div>
      </IonContent>
    </IonPage>
  )
}
